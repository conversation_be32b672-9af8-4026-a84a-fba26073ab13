package main

import (
	"bufio"
	"fmt"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"
	algorithmn "tubes_alpro/Algorithmn"
	cart "tubes_alpro/Cart"
	order "tubes_alpro/Order"
)

func main() {
	scanner := bufio.NewScanner(os.Stdin)
	myCart := cart.Cart{}

	for {
		fmt.Println("\n===== Aplikasi Keranjang Belanja =====")
		fmt.Println("1. Tambah barang ke keranjang")
		fmt.Println("2. Hapus barang dari keranjang")
		fmt.Println("3. Perbarui jumlah barang")
		fmt.Println("4. Lihat keranjang")
		fmt.Println("5. Kosongkan keranjang")
		fmt.Println("6. Urutkan barang di keranjang")
		fmt.Println("7. Cari barang")
		fmt.Println("8. Checkout")
		fmt.Println("9. <PERSON><PERSON><PERSON>")
		fmt.Print("Pilih opsi: ")

		scanner.Scan()
		choice := scanner.Text()

		switch choice {
		case "1":
			addItem(scanner, &myCart)
		case "2":
			removeItem(scanner, &myCart)
		case "3":
			updateItem(scanner, &myCart)
		case "4":
			viewCart(&myCart)
		case "5":
			myCart.ClearCart()
			fmt.Println("Keranjang berhasil dikosongkan!")
		case "6":
			sortCartItems(scanner, &myCart)
		case "7":
			searchItem(scanner, &myCart)
		case "8":
			checkout(scanner, &myCart)
		case "9":
			fmt.Println("Terima kasih telah berbelanja bersama kami!")
			return
		default:
			fmt.Println("Opsi tidak valid. Silakan coba lagi.")
		}
	}
}

func addItem(scanner *bufio.Scanner, c *cart.Cart) {
	var name string
	var quantity, price int

	fmt.Print("Masukkan nama barang: ")
	scanner.Scan()
	name = scanner.Text()

	fmt.Print("Masukkan jumlah: ")
	scanner.Scan()
	quantity, _ = strconv.Atoi(scanner.Text())

	fmt.Print("Masukkan harga per barang: ")
	scanner.Scan()
	price, _ = strconv.Atoi(scanner.Text())

	item := cart.Item{
		Name:     name,
		Quantity: quantity,
		Price:    price,
	}

	c.AddItem(item)
	fmt.Println("Barang berhasil ditambahkan ke keranjang!")
}

func removeItem(scanner *bufio.Scanner, c *cart.Cart) {
	fmt.Print("Masukkan nama barang yang akan dihapus: ")
	scanner.Scan()
	name := scanner.Text()

	c.RemoveItem(name)
	fmt.Println("Barang berhasil dihapus dari keranjang!")
}

func updateItem(scanner *bufio.Scanner, c *cart.Cart) {
	fmt.Print("Masukkan nama barang yang akan diperbarui: ")
	scanner.Scan()
	name := scanner.Text()

	fmt.Print("Masukkan jumlah baru: ")
	scanner.Scan()
	quantity, _ := strconv.Atoi(scanner.Text())

	c.UpdateItem(name, quantity)
	fmt.Println("Jumlah barang berhasil diperbarui!")
}

func viewCart(c *cart.Cart) {
	if len(c.Items) == 0 {
		fmt.Println("Keranjang Anda kosong.")
		return
	}

	fmt.Println("\n===== Keranjang Anda =====")
	totalPrice := 0

	for i, item := range c.Items {
		itemTotal := item.Price * item.Quantity
		totalPrice += itemTotal
		fmt.Printf("%d. %s - Jumlah: %d - Harga: %d - Total: %d\n",
			i+1, item.Name, item.Quantity, item.Price, itemTotal)
	}

	fmt.Printf("\nTotal Nilai Keranjang: %d\n", totalPrice)
}

func checkout(scanner *bufio.Scanner, c *cart.Cart) {
	if len(c.Items) == 0 {
		fmt.Println("Keranjang Anda kosong. Tidak dapat checkout.")
		return
	}

	fmt.Print("Masukkan nama Anda: ")
	scanner.Scan()
	customerName := scanner.Text()

	orderID := fmt.Sprintf("ORD-%d", time.Now().Unix())

	newOrder := order.CreateOrder(orderID, customerName, *c)

	fmt.Println("\n===== Konfirmasi Pesanan =====")
	fmt.Printf("ID Pesanan: %s\n", newOrder.ID)
	fmt.Printf("Pelanggan: %s\n", newOrder.CustomerName)
	fmt.Printf("Tanggal: %s\n", newOrder.OrderDate.Format("2006-01-02 15:04:05"))
	fmt.Printf("Status: %s\n", newOrder.Status)

	fmt.Println("\nBarang:")
	for i, item := range newOrder.Cart.Items {
		fmt.Printf("%d. %s - Jumlah: %d - Harga: %d - Total: %d\n",
			i+1, item.Name, item.Quantity, item.Price, item.Price*item.Quantity)
	}

	fmt.Printf("\nTotal Nilai Pesanan: %d\n", newOrder.TotalPrice)
	fmt.Println("\nTerima kasih atas pesanan Anda!")

	c.ClearCart()
}

func sortCartItems(scanner *bufio.Scanner, c *cart.Cart) {
	if len(c.Items) <= 1 {
		fmt.Println("Keranjang memiliki 1 atau kurang barang. Tidak perlu diurutkan.")
		return
	}

	fmt.Println("\n===== Opsi Pengurutan =====")
	fmt.Println("1. Urutkan berdasarkan harga (rendah ke tinggi)")
	fmt.Println("2. Urutkan berdasarkan harga (tinggi ke rendah)")
	fmt.Println("3. Urutkan berdasarkan nama (A-Z)")
	fmt.Print("Pilih opsi pengurutan: ")

	scanner.Scan()
	sortOption := scanner.Text()

	fmt.Println("\n===== Algoritma Pengurutan =====")
	fmt.Println("1. Selection Sort")
	fmt.Println("2. Insertion Sort")
	fmt.Print("Pilih algoritma pengurutan: ")

	scanner.Scan()
	algorithmOption := scanner.Text()

	if sortOption == "1" || sortOption == "2" {
		prices := make([]int, len(c.Items))
		for i, item := range c.Items {
			prices[i] = item.Price
		}

		if algorithmOption == "1" {
			prices = algorithmn.SelectionSort(prices)
		} else {
			prices = algorithmn.InsertionSort(prices)
		}

		if sortOption == "2" {
			for i, j := 0, len(prices)-1; i < j; i, j = i+1, j-1 {
				prices[i], prices[j] = prices[j], prices[i]
			}
		}

		sortedItems := make([]cart.Item, 0)
		for _, price := range prices {
			for _, item := range c.Items {
				if item.Price == price {
					found := false
					for _, sortedItem := range sortedItems {
						if sortedItem.Name == item.Name && sortedItem.Price == item.Price {
							found = true
							break
						}
					}
					if !found {
						sortedItems = append(sortedItems, item)
					}
				}
			}
		}
		c.Items = sortedItems

	} else if sortOption == "3" {
		sort.Slice(c.Items, func(i, j int) bool {
			return c.Items[i].Name < c.Items[j].Name
		})
	}

	fmt.Println("Barang di keranjang berhasil diurutkan!")
	viewCart(c)
}

func searchItem(scanner *bufio.Scanner, c *cart.Cart) {
	if len(c.Items) == 0 {
		fmt.Println("Keranjang Anda kosong.")
		return
	}

	fmt.Println("\n===== Opsi Pencarian =====")
	fmt.Println("1. Cari berdasarkan nama")
	fmt.Println("2. Cari berdasarkan harga")
	fmt.Print("Pilih opsi pencarian: ")

	scanner.Scan()
	searchOption := scanner.Text()

	fmt.Println("\n===== Algoritma Pencarian =====")
	fmt.Println("1. Linear Search")
	fmt.Println("2. Binary Search (membutuhkan data terurut)")
	fmt.Print("Pilih algoritma pencarian: ")

	scanner.Scan()
	algorithmOption := scanner.Text()

	if searchOption == "1" {
		fmt.Print("Masukkan nama barang yang dicari: ")
		scanner.Scan()
		name := scanner.Text()

		if algorithmOption == "1" {
			found := false
			for i, item := range c.Items {
				if strings.EqualFold(item.Name, name) {
					fmt.Printf("\nBarang ditemukan di posisi %d:\n", i+1)
					fmt.Printf("Nama: %s, Jumlah: %d, Harga: %d\n",
						item.Name, item.Quantity, item.Price)
					found = true
					break
				}
			}
			if !found {
				fmt.Println("Barang tidak ditemukan di keranjang.")
			}
		} else {
			fmt.Println("Binary search berdasarkan nama tidak diimplementasikan.")
		}
	} else if searchOption == "2" {
		fmt.Print("Masukkan harga yang dicari: ")
		scanner.Scan()
		price, _ := strconv.Atoi(scanner.Text())

		if algorithmOption == "1" {
			prices := make([]int, len(c.Items))
			for i, item := range c.Items {
				prices[i] = item.Price
			}

			index := algorithmn.LinearSearch(prices, price)
			if index != -1 {
				fmt.Printf("\nBarang ditemukan di posisi %d:\n", index+1)
				fmt.Printf("Nama: %s, Jumlah: %d, Harga: %d\n",
					c.Items[index].Name, c.Items[index].Quantity, c.Items[index].Price)
			} else {
				fmt.Println("Barang dengan harga tersebut tidak ditemukan di keranjang.")
			}
		} else if algorithmOption == "2" {
			prices := make([]int, len(c.Items))
			for i, item := range c.Items {
				prices[i] = item.Price
			}

			sort.Ints(prices)

			index := algorithmn.BinarySearch(prices, price)
			if index != -1 {
				fmt.Println("\nBarang dengan harga tersebut ada di keranjang.")
				for i, item := range c.Items {
					if item.Price == price {
						fmt.Printf("Posisi %d: %s, Jumlah: %d, Harga: %d\n",
							i+1, item.Name, item.Quantity, item.Price)
					}
				}
			} else {
				fmt.Println("Barang dengan harga tersebut tidak ditemukan di keranjang.")
			}
		}
	}
}
