package order

import (
	"time"
	cart "tubes_alpro/Cart"
)

type Order struct {
	ID           string
	CustomerName string
	Cart         cart.Cart
	TotalPrice   int
	OrderDate    time.Time
	Status       string
}

func CreateOrder(id string, customerName string, c cart.Cart) Order {
	totalPrice := 0
	for _, item := range c.Items {
		totalPrice += item.Price * item.Quantity
	}

	return Order{
		ID:           id,
		CustomerName: customerName,
		Cart:         c,
		TotalPrice:   totalPrice,
		OrderDate:    time.Now(),
		Status:       "pending",
	}
}

func (o *Order) UpdateStatus(status string) {
	o.Status = status
}

func (o *Order) CalculateTotal() {
	totalPrice := 0
	for _, item := range o.Cart.Items {
		totalPrice += item.Price * item.Quantity
	}
	o.TotalPrice = totalPrice
}
